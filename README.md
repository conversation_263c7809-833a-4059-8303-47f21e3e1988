# Android 导航Demo

这是一个简单的Android导航演示应用，展示了如何在多个页面之间进行导航。

## 功能特性

### 页面结构
- **页面A（首页）**: 应用的主页面
- **页面B**: 中间页面
- **页面C**: 最后一个页面

### 导航功能
1. **顺序导航**: A → B → C
2. **返回上一页**: 使用返回按钮回到前一个页面
3. **返回首页**: 从任何页面直接返回到首页

### 按钮说明
- **蓝色按钮**: 跳转到下一页
- **橙色按钮**: 返回上一页
- **红色按钮**: 返回首页

## 技术实现

### 核心组件
- `MainActivity.java`: 页面A的实现
- `ActivityB.java`: 页面B的实现  
- `ActivityC.java`: 页面C的实现
- `activity_main.xml`: 共用的布局文件

### 导航逻辑
- **下一页导航**: 使用`Intent`启动新的Activity
- **返回上一页**: 调用`finish()`方法结束当前Activity
- **返回首页**: 使用`FLAG_ACTIVITY_CLEAR_TOP`标志清除Activity栈

### 按钮可见性控制
- 首页隐藏"返回上一页"和"返回首页"按钮
- 最后一页隐藏"跳转到下一页"按钮
- 中间页面显示所有导航按钮

## 运行要求

- Android SDK API 31+
- Java 17+
- Android Studio 或命令行工具

## 构建和运行

### 使用命令行
```bash
# 设置Java 17环境
export JAVA_HOME=/path/to/java17

# 构建项目
./gradlew build

# 安装到设备
./gradlew installDebug

# 启动应用
adb shell am start -n com.example.study_android/.MainActivity
```

### 运行测试
```bash
# 运行UI自动化测试
./gradlew connectedAndroidTest
```

## 测试覆盖

项目包含完整的UI自动化测试：
- `NavigationTest.java`: 测试页面导航流程
  - 测试A→B→C的顺序导航
  - 测试返回上一页功能
  - 测试返回首页功能
  - 验证按钮可见性

## 项目结构

```
app/src/main/
├── java/com/example/study_android/
│   ├── MainActivity.java      # 页面A
│   ├── ActivityB.java         # 页面B
│   └── ActivityC.java         # 页面C
├── res/
│   ├── layout/
│   │   └── activity_main.xml  # 共用布局
│   └── values/
│       └── strings.xml        # 字符串资源
└── AndroidManifest.xml        # 应用配置

app/src/androidTest/
└── java/com/example/study_android/
    └── NavigationTest.java    # UI测试
```

## 扩展建议

1. **添加动画**: 为页面切换添加过渡动画
2. **数据传递**: 在页面间传递数据
3. **Fragment导航**: 使用Fragment替代Activity
4. **Navigation Component**: 使用Android Jetpack Navigation组件
5. **状态保存**: 添加页面状态保存和恢复功能
