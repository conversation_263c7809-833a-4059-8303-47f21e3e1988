package com.example.study_android;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.activity.OnBackPressedCallback;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import java.util.ArrayList;
import java.util.List;

public class PermissionGuideActivity extends AppCompatActivity {

    private static final int PERMISSION_REQUEST_CODE = 1001;
    private TextView titleText, descriptionText, permissionStatusText;
    private ImageView permissionIcon;
    private Button grantButton, skipButton;
    private List<String> requiredPermissions;
    private int currentStep = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_permission_guide);

        initViews();
        initPermissions();
        updatePermissionStatus();
        updateUIContent();
        checkAndShowNextPermission();
        
        // 注册返回按键处理
        setupBackPressedHandler();
    }

    private void initViews() {
        titleText = findViewById(R.id.titleText);
        descriptionText = findViewById(R.id.descriptionText);
        permissionStatusText = findViewById(R.id.permissionStatusText);
        permissionIcon = findViewById(R.id.permissionIcon);
        grantButton = findViewById(R.id.grantButton);
        skipButton = findViewById(R.id.skipButton);

        grantButton.setOnClickListener(v -> {
            if (checkAllPermissions()) {
                navigateToMainActivity();
            } else {
                requestCurrentPermission();
            }
        });
        skipButton.setOnClickListener(v -> skipCurrentPermission());
    }

    private void initPermissions() {
        requiredPermissions = new ArrayList<>();
        
        // 根据Android版本添加不同的权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 使用新的媒体权限
            requiredPermissions.add(Manifest.permission.READ_MEDIA_IMAGES);
            requiredPermissions.add(Manifest.permission.READ_MEDIA_VIDEO);
            requiredPermissions.add(Manifest.permission.READ_MEDIA_AUDIO);
        } else {
            // Android 12及以下使用传统存储权限
            requiredPermissions.add(Manifest.permission.READ_EXTERNAL_STORAGE);
            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.P) {
                requiredPermissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
            }
        }
    }

    private void checkAndShowNextPermission() {
        // 跳过已授权的权限
        while (currentStep < requiredPermissions.size()) {
            String permission = requiredPermissions.get(currentStep);
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                showPermissionGuide(permission);
                return;
            }
            currentStep++;
        }
        
        // 所有权限都已授权，直接跳转到主页面
        navigateToMainActivity();
    }

    private void showPermissionGuide(String permission) {
        switch (permission) {
            case Manifest.permission.READ_EXTERNAL_STORAGE:
            case Manifest.permission.WRITE_EXTERNAL_STORAGE:
                showStoragePermissionGuide();
                break;
            case Manifest.permission.READ_MEDIA_IMAGES:
            case Manifest.permission.READ_MEDIA_VIDEO:
            case Manifest.permission.READ_MEDIA_AUDIO:
                showMediaPermissionGuide();
                break;
            default:
                showGenericPermissionGuide(permission);
                break;
        }
    }

    private void showStoragePermissionGuide() {
        titleText.setText("📁 文件访问权限");
        descriptionText.setText("为了让您能够上传和管理HTML资源文件，我们需要访问您设备上的文件。\n\n这个权限将用于：\n• 选择和上传HTML文件\n• 保存历史资源文件\n• 管理本地资源库");
        permissionStatusText.setText("当前状态：未授权");
        permissionIcon.setImageResource(android.R.drawable.ic_menu_manage);
        grantButton.setText("授权文件访问");
        grantButton.setVisibility(View.VISIBLE);
        skipButton.setVisibility(View.VISIBLE);
    }

    private void showMediaPermissionGuide() {
        titleText.setText("🖼️ 媒体访问权限");
        descriptionText.setText("为了让您能够选择和上传包含图片、视频等媒体内容的HTML文件，我们需要访问您的媒体文件。\n\n这个权限将用于：\n• 访问HTML文件中的图片资源\n• 上传包含媒体内容的网页\n• 完整的WebView功能体验");
        permissionStatusText.setText("当前状态：未授权");
        permissionIcon.setImageResource(android.R.drawable.ic_menu_gallery);
        grantButton.setText("授权媒体访问");
        grantButton.setVisibility(View.VISIBLE);
        skipButton.setVisibility(View.VISIBLE);
    }

    private void showGenericPermissionGuide(String permission) {
        titleText.setText("🔐 权限请求");
        descriptionText.setText("应用需要以下权限来正常运行：\n" + permission);
        permissionStatusText.setText("当前状态：未授权");
        permissionIcon.setImageResource(android.R.drawable.ic_lock_lock);
        grantButton.setText("授权权限");
        grantButton.setVisibility(View.VISIBLE);
        skipButton.setVisibility(View.VISIBLE);
    }

    private void requestCurrentPermission() {
        if (currentStep < requiredPermissions.size()) {
            String permission = requiredPermissions.get(currentStep);
            
            // 检查是否需要显示权限说明
            if (ActivityCompat.shouldShowRequestPermissionRationale(this, permission)) {
                showPermissionRationale(permission);
            } else {
                ActivityCompat.requestPermissions(this, new String[]{permission}, PERMISSION_REQUEST_CODE);
            }
        }
    }

    private void showPermissionRationale(String permission) {
        String title = "权限说明";
        String message = "此权限对应用功能非常重要，请在系统弹窗中选择'允许'以获得最佳使用体验。";
        
        if (permission.contains("STORAGE") || permission.contains("MEDIA")) {
            message = "文件访问权限是上传和管理HTML资源的核心功能，没有此权限将无法使用文件上传功能。请在系统弹窗中选择'允许'。";
        }
        
        new AlertDialog.Builder(this)
                .setTitle(title)
                .setMessage(message)
                .setPositiveButton("我知道了", (dialog, which) -> {
                    String currentPermission = requiredPermissions.get(currentStep);
                    ActivityCompat.requestPermissions(this, new String[]{currentPermission}, PERMISSION_REQUEST_CODE);
                })
                .setNegativeButton("跳过", (dialog, which) -> skipCurrentPermission())
                .show();
    }

    private void skipCurrentPermission() {
        currentStep++;
        checkAndShowNextPermission();
    }

    private void finishGuide() {
        // 保存引导完成状态
        getSharedPreferences("app_prefs", MODE_PRIVATE)
                .edit()
                .putBoolean("permission_guide_completed", true)
                .apply();
        
        // 跳转到主页面
        navigateToMainActivity();
    }
    
    private void navigateToMainActivity() {
        Intent intent = new Intent(this, MainActivity.class);
        startActivity(intent);
        finish();
    }
    
    private void updatePermissionStatus() {
        boolean hasPermissions = checkAllPermissions();
        
        if (hasPermissions) {
            permissionStatusText.setText("当前状态：已授权");
            permissionStatusText.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
            permissionIcon.setColorFilter(getResources().getColor(android.R.color.holo_green_dark));
            grantButton.setText("进入应用");
        } else {
            permissionStatusText.setText("当前状态：未授权");
            permissionStatusText.setTextColor(getResources().getColor(android.R.color.holo_orange_dark));
            permissionIcon.setColorFilter(getResources().getColor(android.R.color.holo_blue_dark));
            grantButton.setText("授权权限");
        }
    }
    
    private boolean checkAllPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_IMAGES) == PackageManager.PERMISSION_GRANTED &&
                   ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_VIDEO) == PackageManager.PERMISSION_GRANTED &&
                   ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_AUDIO) == PackageManager.PERMISSION_GRANTED;
        } else {
            return ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED &&
                   ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
        }
    }
    
    private void updateUIContent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            titleText.setText("媒体文件访问权限");
            descriptionText.setText("• 📸 访问图片文件：用于上传和管理图片资源\n" +
                                  "• 🎥 访问视频文件：用于上传和管理视频资源\n" +
                                  "• 🎵 访问音频文件：用于上传和管理音频资源\n\n" +
                                  "这些权限将帮助您：\n" +
                                  "✓ 上传本地媒体文件到应用\n" +
                                  "✓ 管理和查看历史上传记录\n" +
                                  "✓ 提供更丰富的内容展示体验");
        } else {
            titleText.setText("存储访问权限");
            descriptionText.setText("• 📁 读取存储权限：用于访问设备上的文件\n" +
                                  "• 💾 写入存储权限：用于保存和管理文件\n\n" +
                                  "这些权限将帮助您：\n" +
                                  "✓ 上传本地文件到应用\n" +
                                  "✓ 保存和管理应用数据\n" +
                                  "✓ 提供完整的文件管理功能");
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == PERMISSION_REQUEST_CODE) {
            // 更新UI状态
            updatePermissionStatus();
            
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // 权限授权成功
                updatePermissionStatus(true);
                currentStep++;
                
                // 延迟一下再检查下一个权限，让用户看到成功状态
                permissionStatusText.postDelayed(() -> checkAndShowNextPermission(), 1000);
            } else {
                // 权限被拒绝
                updatePermissionStatus(false);
                
                // 检查是否被永久拒绝
                String permission = requiredPermissions.get(currentStep);
                if (!ActivityCompat.shouldShowRequestPermissionRationale(this, permission)) {
                    showSettingsDialog();
                } else {
                    // 显示重试选项
                    showRetryDialog();
                }
            }
        }
    }

    private void updatePermissionStatus(boolean granted) {
        if (granted) {
            permissionStatusText.setText("✅ 已授权");
            permissionStatusText.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
            grantButton.setText("继续");
            skipButton.setVisibility(View.GONE);
        } else {
            permissionStatusText.setText("❌ 授权失败");
            permissionStatusText.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
        }
    }

    private void showSettingsDialog() {
        new AlertDialog.Builder(this)
                .setTitle("权限设置")
                .setMessage("权限已被永久拒绝，请在系统设置中手动开启权限，以获得完整的应用功能体验。")
                .setPositiveButton("去设置", (dialog, which) -> {
                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    Uri uri = Uri.fromParts("package", getPackageName(), null);
                    intent.setData(uri);
                    startActivity(intent);
                })
                .setNegativeButton("跳过", (dialog, which) -> skipCurrentPermission())
                .show();
    }

    private void showRetryDialog() {
        new AlertDialog.Builder(this)
                .setTitle("权限被拒绝")
                .setMessage("没有此权限可能会影响应用的部分功能。您可以重新尝试授权或跳过此权限。")
                .setPositiveButton("重新授权", (dialog, which) -> requestCurrentPermission())
                .setNegativeButton("跳过", (dialog, which) -> skipCurrentPermission())
                .show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 从设置页面返回时重新检查权限
        if (currentStep < requiredPermissions.size()) {
            String permission = requiredPermissions.get(currentStep);
            if (ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED) {
                updatePermissionStatus(true);
                currentStep++;
                checkAndShowNextPermission();
            }
        }
    }

    private void setupBackPressedHandler() {
        OnBackPressedCallback callback = new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                // 防止用户在权限引导过程中返回
                new AlertDialog.Builder(PermissionGuideActivity.this)
                        .setTitle("退出应用")
                        .setMessage("权限对应用功能很重要，确定要退出吗？")
                        .setPositiveButton("退出", (dialog, which) -> {
                            finishAffinity();
                        })
                        .setNegativeButton("继续设置", null)
                        .show();
            }
        };
        
        getOnBackPressedDispatcher().addCallback(this, callback);
    }
}