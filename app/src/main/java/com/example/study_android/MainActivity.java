package com.example.study_android;

import android.Manifest;
import android.app.AlertDialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import android.webkit.WebSettings;

import androidx.activity.OnBackPressedCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class MainActivity extends AppCompatActivity {

    private WebView webView;
    private FloatingActionButton fabMenu, fabUpload, fabRefresh, fabHistory, fabFullscreen;
    private boolean isMenuOpen = false;
    private boolean isFullscreen = false;
    private static final int PERMISSION_REQUEST_CODE = 100;
    private ActivityResultLauncher<Intent> filePickerLauncher;
    private File resourcesDir;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 初始化资源目录
        initResourcesDirectory();

        // 初始化文件选择器
        initFilePickerLauncher();

        // 初始化WebView
        initWebView();

        // 初始化悬浮按钮
        initFloatingActionButtons();

        // 检查权限
        checkPermissions();

        // 注册返回按键处理
        setupBackPressedHandler();

    }

    private void initWebView() {
        webView = findViewById(R.id.webView);
    
        // 启用JavaScript
        webView.getSettings().setJavaScriptEnabled(true);
        
        // 允许访问文件
        webView.getSettings().setAllowFileAccess(true);
        webView.getSettings().setAllowFileAccessFromFileURLs(true);
        webView.getSettings().setAllowUniversalAccessFromFileURLs(true);
        
        // 启用DOM存储
        webView.getSettings().setDomStorageEnabled(true);
        
        // 启用数据库
        webView.getSettings().setDatabaseEnabled(true);
        
        // 设置缓存模式
        webView.getSettings().setCacheMode(WebSettings.LOAD_DEFAULT);
        
        // 启用混合内容模式（如果需要）
        webView.getSettings().setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        
        // 设置用户代理
        webView.getSettings().setUserAgentString(webView.getSettings().getUserAgentString() + " MyApp/1.0");
    
        // 设置WebView客户端
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }
            
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                // 页面加载完成后的回调
            }
            
            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                // 处理加载错误
                Toast.makeText(MainActivity.this, "页面加载错误: " + description, Toast.LENGTH_SHORT).show();
            }
        });
    
        // 加载本地HTML文件
        webView.loadUrl("file:///android_asset/index.html");
    }

    private void initResourcesDirectory() {
        resourcesDir = new File(getFilesDir(), "html_resources");
        if (!resourcesDir.exists()) {
            resourcesDir.mkdirs();
        }
    }

    private void initFilePickerLauncher() {
        filePickerLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        Uri uri = result.getData().getData();
                        if (uri != null) {
                            handleFileUpload(uri);
                        }
                    }
                }
        );
    }

    private void initFloatingActionButtons() {
        fabMenu = findViewById(R.id.fabMenu);
        fabUpload = findViewById(R.id.fabUpload);
        fabRefresh = findViewById(R.id.fabRefresh);
        fabHistory = findViewById(R.id.fabHistory);
        fabFullscreen = findViewById(R.id.fabFullscreen);

        fabMenu.setOnClickListener(v -> toggleMenu());
        fabUpload.setOnClickListener(v -> openFilePicker());
        fabRefresh.setOnClickListener(v -> refreshWebView());
        fabHistory.setOnClickListener(v -> showHistoryDialog());
        fabFullscreen.setOnClickListener(v -> toggleFullscreen());
    }

    private void toggleMenu() {
        if (isMenuOpen) {
            closeMenu();
        } else {
            openMenu();
        }
    }

    private void openMenu() {
        isMenuOpen = true;
        fabUpload.setVisibility(View.VISIBLE);
        fabRefresh.setVisibility(View.VISIBLE);
        fabHistory.setVisibility(View.VISIBLE);
        fabFullscreen.setVisibility(View.VISIBLE);

        // 添加动画效果
        fabUpload.animate().translationY(-200f).setDuration(300).start();
        fabRefresh.animate().translationY(-400f).setDuration(300).start();
        fabHistory.animate().translationY(-600f).setDuration(300).start();
        fabFullscreen.animate().translationY(-800f).setDuration(300).start();
        fabMenu.animate().rotation(45f).setDuration(300).start();
    }

    private void closeMenu() {
        isMenuOpen = false;

        // 添加动画效果
        fabUpload.animate().translationY(0f).setDuration(300).withEndAction(() -> fabUpload.setVisibility(View.GONE)).start();
        fabRefresh.animate().translationY(0f).setDuration(300).withEndAction(() -> fabRefresh.setVisibility(View.GONE)).start();
        fabHistory.animate().translationY(0f).setDuration(300).withEndAction(() -> fabHistory.setVisibility(View.GONE)).start();
        fabFullscreen.animate().translationY(0f).setDuration(300).withEndAction(() -> fabFullscreen.setVisibility(View.GONE)).start();
        fabMenu.animate().rotation(0f).setDuration(300).start();
    }

    private void checkPermissions() {
        List<String> permissionsNeeded = new ArrayList<>();

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(Manifest.permission.READ_EXTERNAL_STORAGE);
        }

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }

        if (!permissionsNeeded.isEmpty()) {
            ActivityCompat.requestPermissions(this, permissionsNeeded.toArray(new String[0]), PERMISSION_REQUEST_CODE);
        }
    }

    private void openFilePicker() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("text/html");
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        filePickerLauncher.launch(Intent.createChooser(intent, "选择HTML文件"));
        closeMenu();
    }

    private void handleFileUpload(Uri uri) {
        try {
            InputStream inputStream = getContentResolver().openInputStream(uri);
            if (inputStream != null) {
                // 生成带时间戳的文件名
                String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
                String fileName = "index_" + timestamp + ".html";
                File targetFile = new File(resourcesDir, fileName);

                // 复制文件
                FileOutputStream outputStream = new FileOutputStream(targetFile);
                byte[] buffer = new byte[1024];
                int length;
                while ((length = inputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, length);
                }

                inputStream.close();
                outputStream.close();

                // 设置为当前使用的HTML文件
                File currentFile = new File(resourcesDir, "current.html");
                copyFile(targetFile, currentFile);

                Toast.makeText(this, "资源上传成功！", Toast.LENGTH_SHORT).show();

                // 自动刷新WebView
                refreshWebView();
            }
        } catch (IOException e) {
            e.printStackTrace();
            Toast.makeText(this, "上传失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void copyFile(File source, File target) throws IOException {
        FileInputStream inputStream = new FileInputStream(source);
        FileOutputStream outputStream = new FileOutputStream(target);

        byte[] buffer = new byte[1024];
        int length;
        while ((length = inputStream.read(buffer)) > 0) {
            outputStream.write(buffer, 0, length);
        }

        inputStream.close();
        outputStream.close();
    }

    private void refreshWebView() {
        File currentFile = new File(resourcesDir, "current.html");
        if (currentFile.exists()) {
            webView.loadUrl("file://" + currentFile.getAbsolutePath());
        } else {
            webView.loadUrl("file:///android_asset/index.html");
        }
        Toast.makeText(this, "页面已刷新", Toast.LENGTH_SHORT).show();
        closeMenu();
    }

    private void showHistoryDialog() {
        File[] files = resourcesDir.listFiles((dir, name) -> name.startsWith("index_") && name.endsWith(".html"));

        if (files == null || files.length == 0) {
            Toast.makeText(this, "暂无历史资源", Toast.LENGTH_SHORT).show();
            closeMenu();
            return;
        }

        // 按时间排序
        Arrays.sort(files, (f1, f2) -> Long.compare(f2.lastModified(), f1.lastModified()));

        String[] fileNames = new String[files.length + 1];
        fileNames[0] = "默认资源 (assets/index.html)";

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        for (int i = 0; i < files.length; i++) {
            String timestamp = sdf.format(new Date(files[i].lastModified()));
            fileNames[i + 1] = "上传资源 - " + timestamp;
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("选择要加载的资源")
                .setItems(fileNames, (dialog, which) -> {
                    if (which == 0) {
                        // 加载默认资源
                        webView.loadUrl("file:///android_asset/index.html");
                        // 删除当前文件标记
                        File currentFile = new File(resourcesDir, "current.html");
                        if (currentFile.exists()) {
                            currentFile.delete();
                        }
                    } else {
                        // 加载选中的历史资源
                        File selectedFile = files[which - 1];
                        try {
                            File currentFile = new File(resourcesDir, "current.html");
                            copyFile(selectedFile, currentFile);
                            webView.loadUrl("file://" + currentFile.getAbsolutePath());
                        } catch (IOException e) {
                            e.printStackTrace();
                            Toast.makeText(MainActivity.this, "加载失败", Toast.LENGTH_SHORT).show();
                        }
                    }
                    Toast.makeText(MainActivity.this, "资源已切换", Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton("取消", null)
                .show();

        closeMenu();
    }

    private void setupBackPressedHandler() {
        OnBackPressedCallback callback = new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                if (isMenuOpen) {
                    closeMenu();
                } else if (webView.canGoBack()) {
                    webView.goBack();
                } else {
                    // 如果没有其他处理，则执行默认的返回行为
                    setEnabled(false);
                    getOnBackPressedDispatcher().onBackPressed();
                }
            }
        };

        getOnBackPressedDispatcher().addCallback(this, callback);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            if (!allGranted) {
                Toast.makeText(this, "需要文件访问权限才能使用上传功能", Toast.LENGTH_LONG).show();
            }
        }
    }

    private void toggleFullscreen() {
        if (isFullscreen) {
            exitFullscreen();
        } else {
            enterFullscreen();
        }
        closeMenu();
    }

    private void enterFullscreen() {
        isFullscreen = true;

        // 隐藏状态栏和导航栏
        getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION);

        // 更新按钮图标
        fabFullscreen.setImageResource(android.R.drawable.ic_menu_revert);

        Toast.makeText(this, "已进入全屏模式", Toast.LENGTH_SHORT).show();
    }

    private void exitFullscreen() {
        isFullscreen = false;

        // 显示状态栏和导航栏
        getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);

        // 更新按钮图标
        fabFullscreen.setImageResource(android.R.drawable.ic_menu_crop);

        Toast.makeText(this, "已退出全屏模式", Toast.LENGTH_SHORT).show();
    }
}
