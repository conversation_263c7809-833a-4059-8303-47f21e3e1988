<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center_horizontal">

        <!-- 顶部标题区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="32dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🔐 权限设置向导"
                android:textSize="28sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="为了提供最佳的使用体验，我们需要您的授权"
                android:textSize="16sp"
                android:textColor="@android:color/darker_gray"
                android:gravity="center" />

        </LinearLayout>

        <!-- 权限图标和状态 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@drawable/permission_card_background"
            android:padding="24dp"
            android:layout_marginBottom="24dp">

            <ImageView
                android:id="@+id/permissionIcon"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@android:drawable/ic_lock_lock"
                android:layout_marginBottom="16dp"
                android:tint="@android:color/holo_blue_dark" />

            <TextView
                android:id="@+id/titleText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="权限标题"
                android:textSize="22sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginBottom="8dp"
                android:gravity="center" />

            <TextView
                android:id="@+id/permissionStatusText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="当前状态：未授权"
                android:textSize="14sp"
                android:textColor="@android:color/holo_orange_dark"
                android:background="@drawable/status_background"
                android:padding="8dp"
                android:layout_marginBottom="16dp" />

        </LinearLayout>

        <!-- 权限说明 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/description_background"
            android:padding="20dp"
            android:layout_marginBottom="32dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📋 权限说明"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginBottom="12dp" />

            <TextView
                android:id="@+id/descriptionText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="权限描述内容"
                android:textSize="15sp"
                android:textColor="@android:color/black"
                android:lineSpacingExtra="4dp" />

        </LinearLayout>

        <!-- 操作按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="16dp">

            <Button
                android:id="@+id/grantButton"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="授权权限"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:background="@drawable/primary_button_background"
                android:layout_marginBottom="12dp"
                android:elevation="2dp" />

            <Button
                android:id="@+id/skipButton"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="暂时跳过"
                android:textSize="14sp"
                android:textColor="@android:color/darker_gray"
                android:background="@drawable/secondary_button_background"
                style="?android:attr/borderlessButtonStyle" />

        </LinearLayout>

        <!-- 底部提示 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginTop="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="💡 提示"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/holo_blue_dark"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="您可以随时在应用设置中修改权限配置\n我们承诺严格保护您的隐私和数据安全"
                android:textSize="13sp"
                android:textColor="@android:color/darker_gray"
                android:gravity="center"
                android:lineSpacingExtra="2dp" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>