<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 顶部导航栏 -->

    <!-- WebView 容器 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <WebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <!-- 悬浮操作按钮 -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fabMenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_margin="16dp"
            android:src="@android:drawable/ic_menu_more"
            android:contentDescription="操作菜单" />

        <!-- 上传资源按钮 -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fabUpload"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="88dp"
            android:src="@android:drawable/ic_menu_upload"
            android:contentDescription="上传资源"
            android:visibility="gone"
            android:backgroundTint="@android:color/holo_blue_light" />

        <!-- 刷新按钮 -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fabRefresh"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="160dp"
            android:src="@android:drawable/ic_popup_sync"
            android:contentDescription="手动刷新"
            android:visibility="gone"
            android:backgroundTint="@android:color/holo_green_light" />

        <!-- 历史资源按钮 -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fabHistory"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="232dp"
            android:src="@android:drawable/ic_menu_recent_history"
            android:contentDescription="历史资源"
            android:visibility="gone"
            android:backgroundTint="@android:color/holo_orange_light" />

        <!-- 全屏切换按钮 -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fabFullscreen"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="304dp"
            android:src="@android:drawable/ic_menu_crop"
            android:contentDescription="全屏切换"
            android:visibility="gone"
            android:backgroundTint="@android:color/holo_purple" />

    </FrameLayout>

    <!-- 隐藏的按钮（保持兼容性） -->
    <Button
        android:id="@+id/goBackButton"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <Button
        android:id="@+id/goToHomeButton"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

</LinearLayout>
