<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试上传页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        p {
            font-size: 1.3em;
            margin-bottom: 30px;
        }
        
        .success {
            background: #2ecc71;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: bold;
            margin: 20px 0;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            font-size: 1.1em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 上传测试成功！</h1>
        <div class="success">✅ 这是通过悬浮菜单上传的HTML文件</div>
        <p>恭喜！您已经成功使用了以下功能：</p>
        
        <div class="features">
            <div class="feature">
                📤 <strong>上传资源</strong><br>
                成功上传自定义HTML文件
            </div>
            <div class="feature">
                🔄 <strong>自动刷新</strong><br>
                WebView自动加载新内容
            </div>
            <div class="feature">
                📱 <strong>悬浮菜单</strong><br>
                流畅的动画交互体验
            </div>
            <div class="feature">
                📚 <strong>历史管理</strong><br>
                可以切换不同版本的资源
            </div>
        </div>
        
        <p style="margin-top: 30px; font-size: 1.1em; opacity: 0.9;">
            现在您可以使用悬浮菜单中的"历史资源"功能<br>
            来切换回默认页面或其他上传的资源
        </p>
    </div>
    
    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                feature.style.animationDelay = (index * 0.2) + 's';
                feature.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });
        
        // CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>